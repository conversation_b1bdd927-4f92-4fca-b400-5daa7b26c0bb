# Financial Trading Analysis API [![API Status](https://img.shields.io/badge/API-Active-brightgreen)](http://your-domain.com/docs)

## 🚀 Overview

A Flask-based REST API that provides automated technical analysis and trading signals for various financial instruments including stocks, cryptocurrencies, forex, futures, and index funds. Built with machine learning models and designed for seamless integration with automation platforms like n8n.

**🔗 Live API Documentation:** [http://your-domain.com/docs](http://your-domain.com/docs)

## 🎯 Project Goal

Profitable trading involves extensive knowledge of technical analysis, which can be complex and expensive to access. This API democratizes trading analysis by providing:

- **Automated Technical Analysis** using machine learning models
- **Real-time Trading Signals** with confidence scores
- **Multi-asset Support** (stocks, crypto, forex, futures, indices)
- **Secure API Access** with authentication
- **n8n Integration Ready** for workflow automation

## ✨ Features

### 🔐 Secure API Access
- API key authentication via `X-API-Key` header
- Environment variable configuration
- Comprehensive error handling

### 📊 Multi-Asset Analysis
- **Stocks:** S&P 500, NASDAQ, Dow Jones, international indices
- **Cryptocurrency:** Bitcoin, Ethereum, major altcoins via Binance
- **Forex:** Major currency pairs (EUR/USD, GBP/USD, etc.)
- **Futures & Commodities:** Gold, Oil, Agricultural products
- **Index Funds:** Global market indices

### 🤖 Machine Learning Predictions
- **Action Predictions:** Buy/Sell/Hold recommendations
- **Price Forecasting:** ML-powered price predictions
- **Confidence Scores:** Reliability metrics for each prediction
- **Risk-based Levels:** Support/resistance levels for different risk tolerances

### 🔄 Automation Ready
- **RESTful API** design for easy integration
- **JSON responses** with structured trading signals
- **n8n workflow examples** included
- **Health check endpoint** for monitoring

## 🛠️ Quick Start

### 1. Environment Setup

```bash
# Clone the repository
git clone https://github.com/your-username/trading-analysis-api.git
cd trading-analysis-api

# Install dependencies
pip install -r requirements.txt

# Set API key
export API_KEY="your-secret-api-key-here"

# Run the application
python Trade.py
```

### 2. API Usage

```bash
# Basic stock analysis
curl -X POST http://localhost:5000/analyze \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-secret-api-key" \
  -d '{
    "asset": "Stocks",
    "equity": "AAPL",
    "market": "S&P 500",
    "interval": "1 Day",
    "risk": "Medium"
  }'
```

### 3. Access Documentation

Visit `http://localhost:5000/docs` for complete API documentation with interactive examples.

## 📡 API Endpoints

### `POST /analyze`
Performs comprehensive technical analysis and returns trading signals.

**Parameters:**
- `asset` (string): Asset type - "Stocks", "Cryptocurrency", "Forex", etc.
- `equity` (string, required): Specific instrument (e.g., "AAPL", "BTCUSDT")
- `market` (string): Market/exchange context
- `interval` (string): Time frame - "1 Day", "1 Hour", etc.
- `risk` (string): Risk level - "Low", "Medium", "High"

**Response:**
```json
{
  "status": "success",
  "analysis": {
    "recommended_action": "Buy",
    "current_price": 185.92,
    "predicted_price": 188.45,
    "confidence_scores": {
      "action_confidence": 78.5,
      "price_confidence": 82.1
    },
    "trading_signal": {
      "action": "Buy",
      "target_price": 188.45,
      "stop_loss": 182.15,
      "take_profit": 189.70
    }
  }
}
```

### `GET /health`
Health check endpoint for monitoring API availability.

### `GET /docs`
Interactive API documentation with examples and integration guides.

## 🔄 n8n Integration

### HTTP Request Node Configuration
```json
{
  "method": "POST",
  "url": "http://your-domain.com/analyze",
  "headers": {
    "X-API-Key": "{{$env.TRADING_API_KEY}}"
  },
  "body": {
    "asset": "Stocks",
    "equity": "AAPL",
    "interval": "1 Hour",
    "risk": "Medium"
  }
}
```

### Conditional Logic Example
```javascript
// Execute trade only if confidence > 75%
return items[0].json.analysis.confidence_scores.action_confidence > 75;
```

### Trading Alert Message
```javascript
const analysis = items[0].json.analysis;
return `🚨 ${analysis.recommended_action} ${analysis.equity} at $${analysis.current_price}`;
```

## 🐳 Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

ENV API_KEY=your-secret-key
CMD ["python", "Trade.py"]
```

```bash
# Build and run
docker build -t trading-api .
docker run -p 5000:5000 -e API_KEY=your-secret-key trading-api
```

## 📁 Project Structure

```
├── Trade.py                 # Main Flask API application
├── app/
│   ├── data_sourcing.py     # Market data collection
│   ├── indicator_analysis.py # Technical indicators
│   ├── graph.py             # Visualization components
│   ├── model.py             # ML prediction models
│   └── scaling.py           # Data preprocessing
├── models/
│   ├── action_prediction_model.h5  # Action prediction model
│   └── price_prediction_model.h5   # Price prediction model
├── market_data/             # Market data files
│   ├── stocks.txt
│   ├── binance.txt
│   ├── forex.txt
│   └── futures.txt
├── requirements.txt         # Python dependencies
└── README.md               # This file
```

## 🔧 Configuration

### Environment Variables
```bash
API_KEY=your-secret-api-key-here    # Required for API authentication
PORT=5000                           # Optional: Server port (default: 5000)
```

### Supported Time Intervals
- **Stocks/Forex/Futures:** 5min, 15min, 30min, 1h, 1d, 1w
- **Cryptocurrency:** 1min, 3min, 5min, 15min, 30min, 1h, 6h, 12h, 1d, 1w

## 🛡️ Security & Best Practices

- **API Key Authentication:** All endpoints require valid API key
- **Rate Limiting:** Recommended 60 requests/minute maximum
- **Error Handling:** Comprehensive error responses with status codes
- **Input Validation:** All parameters validated before processing
- **Confidence Thresholds:** Set minimum confidence levels for automated trading

## 📊 Monitoring & Logging

- **Health Check:** `/health` endpoint for uptime monitoring
- **Error Tracking:** Detailed error messages in responses
- **Performance:** Built-in garbage collection for memory management

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This API provides educational and informational trading signals based on technical analysis and machine learning models. **This is not financial advice.** Always:

- Conduct your own research
- Consider consulting with financial advisors
- Understand that past performance doesn't guarantee future results
- Use proper risk management in all trading activities
- Start with small amounts when testing automated strategies

## 🆘 Support

- **Documentation:** [http://your-domain.com/docs](http://your-domain.com/docs)
- **Issues:** [GitHub Issues](https://github.com/your-username/trading-analysis-api/issues)
- **Discussions:** [GitHub Discussions](https://github.com/your-username/trading-analysis-api/discussions)

---

**Built with ❤️ using Flask, TensorFlow, and Machine Learning**
