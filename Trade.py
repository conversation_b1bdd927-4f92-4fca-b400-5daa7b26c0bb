from flask import Flask, request, jsonify, render_template_string
import os
import pandas as pd
from app.data_sourcing import Data_Sourcing, data_update
from app.indicator_analysis import Indications
from app.graph import Visualization
from tensorflow.keras.models import load_model
import gc
import warnings

app = Flask(__name__)
warnings.filterwarnings("ignore")

# Load models globally
action_model = load_model("models/action_prediction_model.h5")
price_model = load_model("models/price_prediction_model.h5")
app_data = Data_Sourcing()

# HTML template for documentation
DOCS_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Analysis API Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            font-size: 2.5em;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            margin-top: 40px;
            font-size: 1.8em;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #7f8c8d;
            font-size: 1.3em;
            margin-top: 25px;
        }
        h4 {
            color: #5a6c7d;
            font-size: 1.1em;
            margin-top: 20px;
        }
        code {
            background: #f8f9fa;
            color: #e74c3c;
            padding: 3px 8px;
            border-radius: 4px;
            font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
            font-size: 0.9em;
            border: 1px solid #e9ecef;
        }
        pre {
            background: #1e1e1e;
            color: #f8f8f2;
            padding: 25px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 20px 0;
            border: 1px solid #444;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
        }
        pre code {
            background: transparent !important;
            color: #f8f8f2 !important;
            padding: 0 !important;
            border: none !important;
            font-size: 0.95em;
            line-height: 1.5;
        }
        pre::before {
            content: "💻";
            position: absolute;
            top: 8px;
            right: 12px;
            opacity: 0.6;
        }
        .endpoint {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
            padding: 20px;
            border-left: 5px solid #27ae60;
            margin: 25px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
        }
        .method {
            background: #3498db;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.85em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .method.post { background: linear-gradient(135deg, #e67e22, #d35400); }
        .method.get { background: linear-gradient(135deg, #27ae60, #229954); }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #e9ecef;
            padding: 15px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            font-weight: 600;
            color: #495057;
            text-transform: uppercase;
            font-size: 0.85em;
            letter-spacing: 0.5px;
        }
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        tr:hover {
            background: #e3f2fd;
            transition: background 0.3s ease;
        }
        .error {
            background: linear-gradient(135deg, #fdf2f2, #fce4ec);
            border-left: 5px solid #e74c3c;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .success {
            background: linear-gradient(135deg, #f0f9f0, #e8f5e8);
            border-left: 5px solid #27ae60;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .warning {
            background: linear-gradient(135deg, #fef9e7, #fff3cd);
            border-left: 5px solid #f39c12;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .nav {
            position: sticky;
            top: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 30px;
            border-radius: 8px;
            text-align: center;
        }
        .nav a {
            margin: 0 15px;
            color: #3498db;
            text-decoration: none;
            font-weight: 600;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        .nav a:hover {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
        }
        .badge {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 600;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin: 8px 0;
            color: #555;
        }
        footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
            text-align: center;
            color: #7f8c8d;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav">
            <a href="#overview">Overview</a>
            <a href="#authentication">Authentication</a>
            <a href="#endpoints">Endpoints</a>
            <a href="#examples">Examples</a>
            <a href="#n8n">n8n Integration</a>
            <a href="#errors">Error Handling</a>
        </div>

        <h1 id="overview">🚀 Trading Analysis API Documentation</h1>
        
        <div class="success">
            <strong>API Status:</strong> <span class="badge">Active</span> | 
            <strong>Version:</strong> 1.0 | 
            <strong>Base URL:</strong> <code>{{ base_url }}</code>
        </div>

        <p>The Trading Analysis API provides automated technical analysis and trading signals for various financial instruments including stocks, cryptocurrencies, forex, futures, and index funds. Built with Flask and powered by machine learning models, this API delivers actionable trading recommendations with confidence scores.</p>

        <h2 id="authentication">🔐 Authentication</h2>
        <p>All API requests require authentication using an API key passed in the request header.</p>
        
        <table>
            <tr><th>Header</th><th>Value</th><th>Required</th></tr>
            <tr><td><code>Content-Type</code></td><td><code>application/json</code></td><td>✅ Yes</td></tr>
            <tr><td><code>X-API-Key</code></td><td>Your API key</td><td>✅ Yes</td></tr>
        </table>

        <h2 id="endpoints">📡 Endpoints</h2>

        <div class="endpoint">
            <h3><span class="method post">POST</span> /analyze</h3>
            <p>Performs comprehensive technical analysis and returns trading signals with price predictions.</p>
        </div>

        <h4>Request Parameters</h4>
        <table>
            <tr><th>Parameter</th><th>Type</th><th>Required</th><th>Default</th><th>Description</th></tr>
            <tr><td><code>asset</code></td><td>string</td><td>No</td><td>"Stocks"</td><td>Asset type: "Stocks", "Cryptocurrency", "Forex", "Index Fund", "Futures & Commodities"</td></tr>
            <tr><td><code>equity</code></td><td>string</td><td>✅ Yes</td><td>-</td><td>Specific instrument (e.g., "AAPL", "BTCUSDT")</td></tr>
            <tr><td><code>market</code></td><td>string</td><td>No</td><td>Auto-detected</td><td>Market/exchange (e.g., "S&P 500", "USDT")</td></tr>
            <tr><td><code>interval</code></td><td>string</td><td>No</td><td>"1 Day"</td><td>Time interval for analysis</td></tr>
            <tr><td><code>risk</code></td><td>string</td><td>No</td><td>"Medium"</td><td>Risk level: "Low", "Medium", "High"</td></tr>
        </table>

        <h4>Example Request</h4>
        <pre><code>curl -X POST {{ base_url }}/analyze \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: your-secret-api-key" \\
  -d '{
    "asset": "Stocks",
    "equity": "AAPL",
    "market": "S&P 500",
    "interval": "1 Day",
    "risk": "Medium"
  }'</code></pre>

        <h4>Success Response (200 OK)</h4>
        <pre><code>{
  "status": "success",
  "analysis": {
    "asset": "S&P 500 Companies",
    "equity": "AAPL",
    "exchange": "Yahoo! Finance",
    "interval": "1 Day",
    "prediction_date": "2024-01-15 16:00:00",
    "current_price": 185.92,
    "currency": "USD",
    "price_change_percent": 1.25,
    "predicted_price": 188.45,
    "recommended_action": "Buy",
    "confidence_scores": {
      "action_confidence": 78.5,
      "price_confidence": 82.1
    },
    "risk_levels": {
      "risk_setting": "Medium",
      "buy_price": 182.15,
      "sell_price": 189.70
    },
    "trading_signal": {
      "action": "Buy",
      "confidence": 78.5,
      "target_price": 188.45,
      "stop_loss": 182.15,
      "take_profit": 189.70
    }
  }
}</code></pre>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /assets</h3>
            <p>Retrieve all available assets organized by category with optional filtering.</p>
        </div>

        <h4>Query Parameters</h4>
        <table>
            <tr><th>Parameter</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td><code>type</code></td><td>string</td><td>No</td><td>Filter by asset type: "Stocks", "Cryptocurrency", "Forex", "Futures & Commodities", "Index Funds"</td></tr>
            <tr><td><code>market</code></td><td>string</td><td>No</td><td>Filter by market name (partial match)</td></tr>
            <tr><td><code>limit</code></td><td>integer</td><td>No</td><td>Limit number of results per category</td></tr>
        </table>

        <h4>Example Request</h4>
        <pre><code>curl -X GET "{{ base_url }}/assets?type=Stocks&market=S%26P%20500&limit=10" \
  -H "X-API-Key: your-secret-api-key"</code></pre>

        <h4>Success Response (200 OK)</h4>
        <pre><code>{
  "status": "success",
  "total_assets": 503,
  "asset_categories": {
    "Stocks": {
      "US S&P 500": {
        "count": 10,
        "assets": [
          {
            "symbol": "3M",
            "ticker": "MMM",
            "currency": "USD",
            "currency_name": "US Dollar"
          },
          {
            "symbol": "Apple Inc.",
            "ticker": "AAPL",
            "currency": "USD",
            "currency_name": "US Dollar"
          }
        ]
      }
    }
  },
  "filters_applied": {
    "asset_type": "Stocks",
    "market": "S&P 500",
    "limit": 10
  },
  "available_asset_types": [
    "Stocks",
    "Cryptocurrency",
    "Forex",
    "Futures & Commodities",
    "Index Funds"
  ]
}</code></pre>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /health</h3>
            <p>Health check endpoint to verify API availability.</p>
        </div>

        <h4>Response (200 OK)</h4>
        <pre><code>{
  "status": "healthy",
  "service": "trading-analysis-api"
}</code></pre>

        <h2 id="examples">💡 Usage Examples</h2>

        <h3>List All Assets</h3>
        <pre><code>curl -X GET "{{ base_url }}/assets" \
  -H "X-API-Key: your-secret-api-key"</code></pre>

        <h3>List Cryptocurrency Assets</h3>
        <pre><code>curl -X GET "{{ base_url }}/assets?type=Cryptocurrency" \
  -H "X-API-Key: your-secret-api-key"</code></pre>

        <h3>List S&P 500 Stocks (Limited)</h3>
        <pre><code>curl -X GET "{{ base_url }}/assets?type=Stocks&market=S%26P%20500&limit=50" \
  -H "X-API-Key: your-secret-api-key"</code></pre>

        <h3>Stock Analysis</h3>
        <pre><code>{
  "asset": "Stocks",
  "equity": "AAPL",
  "market": "S&P 500",
  "interval": "1 Hour",
  "risk": "Medium"
}</code></pre>

        <h3>Cryptocurrency Analysis</h3>
        <pre><code>{
  "asset": "Cryptocurrency",
  "equity": "BTCUSDT",
  "market": "USDT",
  "interval": "1 Day",
  "risk": "High"
}</code></pre>

        <h3>Forex Analysis</h3>
        <pre><code>{
  "asset": "Forex",
  "equity": "EUR/USD",
  "interval": "1 Hour",
  "risk": "Low"
}</code></pre>

        <h2 id="n8n">🔄 n8n Integration</h2>

        <h3>HTTP Request Node Configuration</h3>
        <pre><code>{
  "method": "POST",
  "url": "{{ base_url }}/analyze",
  "headers": {
    "X-API-Key": "{% raw %}{{$env.TRADING_API_KEY}}{% endraw %}"
  },
  "body": {
    "asset": "Stocks",
    "equity": "AAPL",
    "interval": "1 Hour",
    "risk": "Medium"
  }
}</code></pre>

        <h3>Conditional Logic (IF Node)</h3>
        <pre><code>// Check if action is Buy and confidence > 75%
return items[0].json.analysis.recommended_action === 'Buy' && 
       items[0].json.analysis.confidence_scores.action_confidence > 75;</code></pre>

        <h3>Trading Alert Message</h3>
        <pre><code>// Format trading alert message
const analysis = items[0].json.analysis;
return {% raw %}`🚨 Trading Alert: ${analysis.recommended_action} ${analysis.equity}
Current: $${analysis.current_price}
Target: $${analysis.predicted_price}
Confidence: ${analysis.confidence_scores.action_confidence}%`{% endraw %};</code></pre>

        <h2 id="errors">⚠️ Error Handling</h2>

        <div class="error">
            <h4>401 Unauthorized</h4>
            <pre><code>{ "error": "Invalid or missing API key" }</code></pre>
        </div>

        <div class="error">
            <h4>400 Bad Request</h4>
            <pre><code>{ "error": "equity parameter is required" }</code></pre>
        </div>

        <div class="error">
            <h4>500 Internal Server Error</h4>
            <pre><code>{ "error": "Analysis failed: [specific error message]" }</code></pre>
        </div>

        <h2>📊 Supported Assets & Intervals</h2>

        <h3>Asset Types</h3>
        <ul>
            <li><strong>Stocks:</strong> S&P 500, NASDAQ, Dow Jones, and international indices</li>
            <li><strong>Cryptocurrency:</strong> Bitcoin, Ethereum, and major altcoins</li>
            <li><strong>Forex:</strong> Major currency pairs (EUR/USD, GBP/USD, etc.)</li>
            <li><strong>Futures & Commodities:</strong> Gold, Oil, Agricultural products</li>
            <li><strong>Index Funds:</strong> Major market indices</li>
        </ul>

        <h3>Time Intervals</h3>
        <div class="warning">
            <strong>Stocks/Forex/Futures/Index Funds:</strong> 5 Minute, 15 Minute, 30 Minute, 1 Hour, 1 Day, 1 Week<br>
            <strong>Cryptocurrency:</strong> 1 Minute, 3 Minute, 5 Minute, 15 Minute, 30 Minute, 1 Hour, 6 Hour, 12 Hour, 1 Day, 1 Week
        </div>

        <h2>🛡️ Best Practices</h2>
        <ul>
            <li><strong>Rate Limiting:</strong> Recommended maximum 60 requests per minute</li>
            <li><strong>Confidence Thresholds:</strong> Set minimum confidence levels (e.g., 75%) for automated actions</li>
            <li><strong>Risk Management:</strong> Always implement stop-loss and take-profit levels</li>
            <li><strong>Error Handling:</strong> Implement proper try-catch blocks in your workflows</li>
            <li><strong>Monitoring:</strong> Log all API responses for audit and debugging</li>
        </ul>

        <div class="warning">
            <strong>⚠️ Disclaimer:</strong> This API provides educational and informational trading signals. Always conduct your own research and consider consulting with financial advisors before making investment decisions. Past performance does not guarantee future results.
        </div>

        <footer>
            <p>🚀 Trading Analysis API | Built with Flask & Machine Learning | Version 1.0</p>
        </footer>
    </div>
</body>
</html>
"""

def authenticate_api_key():
    """Validate API key from X-API-Key header"""
    api_key = os.getenv('API_KEY')
    if not api_key:
        return False
    
    provided_key = request.headers.get('X-API-Key')
    return provided_key == api_key

@app.route('/')
@app.route('/docs')
def documentation():
    """Serve API documentation page"""
    base_url = request.url_root.rstrip('/')
    return render_template_string(DOCS_TEMPLATE, base_url=base_url)

@app.route('/analyze', methods=['POST'])
def analyze():
    # Authenticate API key
    if not authenticate_api_key():
        return jsonify({"error": "Invalid or missing API key"}), 401
    
    gc.collect()
    
    try:
        # Get parameters from request JSON
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        # Extract parameters with defaults
        asset = data.get('asset', 'Stocks')
        risk = data.get('risk', 'Medium')
        interval = data.get('interval', '1 Day')
        equity = data.get('equity')
        market = data.get('market')
        
        if not equity:
            return jsonify({"error": "equity parameter is required"}), 400
        
        indication = 'Predicted'
        
        # Process asset type and set exchange
        if asset in ['Index Fund', 'Forex', 'Futures & Commodities', 'Stocks']:
            exchange = 'Yahoo! Finance'
            app_data.exchange_data(exchange)
            
            if asset == 'Stocks':
                if not market:
                    market = 'S&P 500'  # Default market
                app_data.market_data(market)
                assets = app_data.stocks
                asset_label = f'{market} Companies'
            elif asset == 'Index Fund':
                assets = app_data.indexes
                asset_label = asset
            elif asset == 'Futures & Commodities':
                assets = app_data.futures
                asset_label = asset
            elif asset == 'Forex':
                assets = app_data.forex
                asset_label = asset
            
            # Set currency and market based on asset type
            if asset == 'Futures & Commodities':
                currency = 'USD'
                market = None
            elif asset == 'Index Fund':
                currency = 'Pts'
                market = None
            elif asset == 'Forex':
                currency = app_data.df_forex[(app_data.df_forex['Currencies'] == equity)]['Currency'].unique()[0]
                market = app_data.df_forex[(app_data.df_forex['Currencies'] == equity)]['Market'].unique()[0]
            elif asset == f'{market} Companies':
                currency = app_data.df_stocks[((app_data.df_stocks['Company'] == equity) & (app_data.df_stocks['Index Fund'] == market))]['Currency'].unique()[0]
                asset = 'Stock'
            
            volitility_index = 0
            
        elif asset in ['Cryptocurrency']:
            exchange = 'Binance'
            app_data.exchange_data(exchange)
            markets = app_data.markets
            
            if not market:
                market = 'USDT'  # Default market
            app_data.market_data(market)
            assets = app_data.assets
            currency = app_data.currency
            asset_label = asset
            volitility_index = 2
        
        # Perform analysis
        analysis = Visualization(exchange, interval, equity, indication, action_model, price_model, market)
        analysis_day = Indications(exchange, '1 Day', equity, market)
        
        # Extract results
        requested_date = str(analysis.df.index[-1])
        current_price = float(analysis.df['Adj Close'][-1])
        change = float(analysis.df['Adj Close'].pct_change()[-1]) * 100
        requested_prediction_price = float(analysis.requested_prediction_price)
        requested_prediction_action = analysis.requested_prediction_action
        
        # Calculate risk levels
        risks = {
            'Low': [analysis_day.df['S1'].values[-1], analysis_day.df['R1'].values[-1]], 
            'Medium': [analysis_day.df['S2'].values[-1], analysis_day.df['R2'].values[-1]],   
            'High': [analysis_day.df['S3'].values[-1], analysis_day.df['R3'].values[-1]]
        }
        buy_price = float(risks[risk][0])
        sell_price = float(risks[risk][1])
        
        # Format response
        response_data = {
            "status": "success",
            "analysis": {
                "asset": asset_label,
                "equity": equity,
                "exchange": exchange,
                "interval": interval,
                "prediction_date": requested_date,
                "current_price": current_price,
                "currency": currency,
                "price_change_percent": round(change, 2),
                "predicted_price": requested_prediction_price,
                "recommended_action": requested_prediction_action,
                "confidence_scores": {
                    "action_confidence": analysis.score_action,
                    "price_confidence": analysis.score_price
                },
                "risk_levels": {
                    "risk_setting": risk,
                    "buy_price": buy_price,
                    "sell_price": sell_price
                },
                "trading_signal": {
                    "action": requested_prediction_action,
                    "confidence": analysis.score_action,
                    "target_price": requested_prediction_price,
                    "stop_loss": buy_price if requested_prediction_action == "Buy" else sell_price,
                    "take_profit": sell_price if requested_prediction_action == "Buy" else buy_price
                }
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        return jsonify({"error": f"Analysis failed: {str(e)}"}), 500

@app.route('/assets', methods=['GET'])
def list_assets():
    """List all available assets by category"""
    # Authenticate API key
    if not authenticate_api_key():
        return jsonify({"error": "Invalid or missing API key"}), 401

    try:
        # Get query parameters for filtering
        asset_type = request.args.get('type', None)  # Optional filter by asset type
        market = request.args.get('market', None)    # Optional filter by market
        limit = request.args.get('limit', None)      # Optional limit results

        # Convert limit to integer if provided
        if limit:
            try:
                limit = int(limit)
                if limit <= 0:
                    return jsonify({"error": "Limit must be a positive integer"}), 400
            except ValueError:
                return jsonify({"error": "Limit must be a valid integer"}), 400

        assets_data = {
            "status": "success",
            "total_assets": 0,
            "asset_categories": {}
        }

        # Load and process Stocks
        if not asset_type or asset_type.lower() in ['stocks', 'stock']:
            try:
                df_stocks = app_data.df_stocks
                stocks_by_market = {}

                for index_fund in df_stocks['Index Fund'].unique():
                    if pd.notna(index_fund):
                        market_stocks = df_stocks[df_stocks['Index Fund'] == index_fund]
                        if not market or market.lower() in index_fund.lower():
                            stock_list = []
                            for _, row in market_stocks.iterrows():
                                if pd.notna(row['Company']) and pd.notna(row['Ticker']):
                                    stock_list.append({
                                        "symbol": row['Company'],
                                        "ticker": row['Ticker'],
                                        "currency": row['Currency'] if pd.notna(row['Currency']) else 'USD',
                                        "currency_name": row['Currency_Name'] if pd.notna(row['Currency_Name']) else 'US Dollar'
                                    })

                            if stock_list:
                                if limit:
                                    stock_list = stock_list[:limit]
                                stocks_by_market[index_fund] = {
                                    "count": len(stock_list),
                                    "assets": stock_list
                                }

                if stocks_by_market:
                    assets_data["asset_categories"]["Stocks"] = stocks_by_market
                    assets_data["total_assets"] += sum(market["count"] for market in stocks_by_market.values())
            except Exception as e:
                print(f"Error loading stocks: {e}")

        # Load and process Cryptocurrency
        if not asset_type or asset_type.lower() in ['cryptocurrency', 'crypto']:
            try:
                df_crypto = app_data.df_crypto
                crypto_by_market = {}

                for market_type in df_crypto['Market'].unique():
                    if pd.notna(market_type):
                        market_crypto = df_crypto[df_crypto['Market'] == market_type]
                        if not market or market.lower() in market_type.lower():
                            crypto_list = []
                            for _, row in market_crypto.iterrows():
                                if pd.notna(row['Currency']) and pd.notna(row['Binance Pair']):
                                    crypto_list.append({
                                        "symbol": row['Currency'],
                                        "pair": row['Binance Pair'],
                                        "market": row['Market']
                                    })

                            if crypto_list:
                                if limit:
                                    crypto_list = crypto_list[:limit]
                                crypto_by_market[market_type] = {
                                    "count": len(crypto_list),
                                    "assets": crypto_list
                                }

                if crypto_by_market:
                    assets_data["asset_categories"]["Cryptocurrency"] = crypto_by_market
                    assets_data["total_assets"] += sum(market["count"] for market in crypto_by_market.values())
            except Exception as e:
                print(f"Error loading cryptocurrency: {e}")

        # Load and process Forex
        if not asset_type or asset_type.lower() == 'forex':
            try:
                df_forex = app_data.df_forex
                forex_list = []

                for _, row in df_forex.iterrows():
                    if pd.notna(row['Currencies']) and pd.notna(row['Ticker']):
                        if not market or market.lower() in row['Currencies'].lower():
                            forex_list.append({
                                "symbol": row['Currencies'],
                                "ticker": row['Ticker'],
                                "base_currency": row['Currency'] if pd.notna(row['Currency']) else '',
                                "quote_currency": row['Market'] if pd.notna(row['Market']) else ''
                            })

                if forex_list:
                    if limit:
                        forex_list = forex_list[:limit]
                    assets_data["asset_categories"]["Forex"] = {
                        "count": len(forex_list),
                        "assets": forex_list
                    }
                    assets_data["total_assets"] += len(forex_list)
            except Exception as e:
                print(f"Error loading forex: {e}")

        # Load and process Futures & Commodities
        if not asset_type or asset_type.lower() in ['futures', 'commodities', 'futures & commodities']:
            try:
                df_futures = app_data.df_futures
                futures_list = []

                for _, row in df_futures.iterrows():
                    if pd.notna(row['Futures']) and pd.notna(row['Ticker']):
                        if not market or market.lower() in row['Futures'].lower():
                            futures_list.append({
                                "symbol": row['Futures'],
                                "ticker": row['Ticker']
                            })

                if futures_list:
                    if limit:
                        futures_list = futures_list[:limit]
                    assets_data["asset_categories"]["Futures & Commodities"] = {
                        "count": len(futures_list),
                        "assets": futures_list
                    }
                    assets_data["total_assets"] += len(futures_list)
            except Exception as e:
                print(f"Error loading futures: {e}")

        # Load and process Index Funds
        if not asset_type or asset_type.lower() in ['index', 'indexes', 'index fund', 'index funds']:
            try:
                df_indexes = app_data.df_indexes
                indexes_list = []

                for _, row in df_indexes.iterrows():
                    if pd.notna(row['Indexes']) and pd.notna(row['Ticker']):
                        if not market or market.lower() in row['Indexes'].lower():
                            indexes_list.append({
                                "symbol": row['Indexes'],
                                "ticker": row['Ticker']
                            })

                if indexes_list:
                    if limit:
                        indexes_list = indexes_list[:limit]
                    assets_data["asset_categories"]["Index Funds"] = {
                        "count": len(indexes_list),
                        "assets": indexes_list
                    }
                    assets_data["total_assets"] += len(indexes_list)
            except Exception as e:
                print(f"Error loading indexes: {e}")

        # Add metadata
        assets_data["filters_applied"] = {
            "asset_type": asset_type,
            "market": market,
            "limit": limit
        }

        assets_data["available_asset_types"] = [
            "Stocks",
            "Cryptocurrency",
            "Forex",
            "Futures & Commodities",
            "Index Funds"
        ]

        return jsonify(assets_data)

    except Exception as e:
        return jsonify({"error": f"Failed to retrieve assets: {str(e)}"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "service": "trading-analysis-api"})

@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    # For development
    app.run(debug=True, host='0.0.0.0', port=5000)
